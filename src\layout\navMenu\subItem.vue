<template>
  <template v-for="val in chils">
    <el-sub-menu :index="val.path" :key="val.path" v-if="val.children && val.children.length > 0">
      <template #title>
        <SvgIcon :name="val.meta.icon" :type="'menu'" :color="''" />
        <span>{{ val.meta.title }}</span>
      </template>
      <sub-item :chil="val.children" />
    </el-sub-menu>
    <el-menu-item
        :index="val.path"
        :key="val.path"
        v-else-if="!val.meta.link || val.meta.isIframe"
    >
      <template #title>
        <SvgIcon :name="val.meta.icon" :type="'menu'" :color="''" />
        <span>{{ val.meta.title }}</span>
      </template>
    </el-menu-item>

    <!-- 普通外链 (http/https) -->
    <li
        class="el-menu-item external-link-item"
        :key="val.path"
        v-else-if="(val.meta?.link && /^https?:\/\//i.test(val.meta.link)) || (val.meta?.isLink && /^https?:\/\//i.test(val.meta.isLink))"
        @click="handleMenuItemClick(val)"
    >
      <a :href="val.meta?.link || val.meta?.isLink" target="_blank" rel="noopener noreferrer" class="external-link">
        <SvgIcon :name="val.meta.icon" :type="'menu'" :color="''" />
        <span>{{ val.meta.title }}</span>
      </a>
    </li>

  </template>
</template>

<script setup lang="ts" name="navMenuSubItem">
import { computed } from 'vue';
import { RouteRecordRaw } from 'vue-router';
import other from '/@/utils/other';
import router from "@/router";

// 定义父组件传过来的值
const props = defineProps({
	// 菜单列表
	chil: {
		type: Array<RouteRecordRaw>,
		default: () => [],
	},
});

const handleMenuItemClick = (menuItem: any) => {
  const link = menuItem.meta?.link || menuItem.meta?.isLink;
  const target = menuItem.meta?.target || '_blank'; // 默认在新标签页打开

  if (link && /^https?:\/\//i.test(link)) {
    // 在新标签页打开，安全且不干扰原页面
    window.open(link, target, 'noopener,noreferrer');
  } else {
    router.push(menuItem.path);
  }
};
// 获取父级菜单数据
const chils = computed(() => {
	return <RouteItems>props.chil;
});
// 打开外部链接
const onALinkClick = (val: RouteItem) => {
	other.handleOpenLink(val);
};
</script>
